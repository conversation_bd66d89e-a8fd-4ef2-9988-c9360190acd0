// lib/src/features/home/<USER>/pages/watch_page.dart

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/discovery/bloc/discovery_bloc.dart';
import 'package:xinglian/src/features/discovery/repository/discovery_repository.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_bloc.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_repository.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_models.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/interactive_story/models/interactive_story_model.dart';
import 'package:xinglian/src/core/networking/api_client.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/features/auth/bloc/auth_bloc.dart';
import 'package:xinglian/src/features/chat/repository/chat_repository.dart';

class WatchPage extends StatefulWidget {
  const WatchPage({super.key});

  @override
  State<WatchPage> createState() => _WatchPageState();
}

class _WatchPageState extends State<WatchPage> {
  final PageController _bannerController = PageController();
  int _currentBannerIndex = 0;

  @override
  void dispose() {
    _bannerController.dispose();
    super.dispose();
  }

  // 安全获取对象属性的方法
  dynamic _safeGetProperty(dynamic obj, String propertyName) {
    try {
      // 使用反射安全访问属性
      switch (propertyName) {
        case 'id':
          return obj.id;
        case 'name':
          return obj.name;
        case 'imageUrl':
          return obj.imageUrl;
        case 'description':
          return obj.description;
        case 'dialogueCount':
          return obj.dialogueCount;
        case 'title':
          return obj.title;
        case 'coverUrl':
          return obj.coverUrl;
        case 'viewCount':
          return obj.viewCount;
        default:
          return null;
      }
    } catch (e) {
      // 如果属性不存在，返回null
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider(
          create: (context) => RecommendationRepository(context.read<ApiClient>()),
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => RecommendationBloc(
              context.read<RecommendationRepository>(),
            )..add(LoadInitialRecommendations()),
          ),
          BlocProvider(
            create: (context) => DiscoveryBloc(context.read<DiscoveryRepository>()),
          ),
        ],
        child: Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            backgroundColor: AppColors.background,
            elevation: 0,
            automaticallyImplyLeading: false,
            title: _buildSearchBar(),
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined, color: AppColors.primaryText),
                onPressed: () {
                  // TODO: 跳转到通知页面
                },
              ),
              IconButton(
                icon: const Icon(Icons.person_outline, color: AppColors.primaryText),
                onPressed: () {
                  // TODO: 跳转到个人中心
                },
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                _buildBannerSection(),
                _buildFunctionEntries(),
                _buildContentSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return GestureDetector(
      onTap: () => context.push('/search'),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(18),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            height: 36,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: AppColors.inputBackground.withOpacity(0.5),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                const Icon(Icons.search, color: AppColors.secondaryText, size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text('搜索角色、剧情...', style: TextStyle(color: AppColors.secondaryText, fontSize: 14)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建轮播Banner
  Widget _buildBannerSection() {
    final banners = [
      {
        'image': 'https://i.imgur.com/banner1.png',
        'title': '新角色上线',
        'subtitle': '与TA开启全新冒险',
      },
      {
        'image': 'https://i.imgur.com/banner2.png',
        'title': '限时活动',
        'subtitle': '参与即可获得专属奖励',
      },
      {
        'image': 'https://i.imgur.com/banner3.png',
        'title': '热门剧情',
        'subtitle': '体验精彩互动故事',
      },
    ];

    return Container(
      height: 180,
      margin: const EdgeInsets.all(16),
      child: Stack(
        children: [
          PageView.builder(
            controller: _bannerController,
            onPageChanged: (index) {
              setState(() {
                _currentBannerIndex = index;
              });
            },
            itemCount: banners.length,
            itemBuilder: (context, index) {
              final banner = banners[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.accentPurple.withOpacity(0.8),
                      AppColors.accentBlue.withOpacity(0.8),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        banner['image']!,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.accentPurple.withOpacity(0.8),
                                  AppColors.accentBlue.withOpacity(0.8),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    Positioned(
                      bottom: 16,
                      left: 16,
                      right: 16,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            banner['title']!,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            banner['subtitle']!,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          Positioned(
            bottom: 12,
            right: 16,
            child: Row(
              children: banners.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentBannerIndex == entry.key
                        ? Colors.white
                        : Colors.white.withOpacity(0.4),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionEntries() {
    final functions = [
      {'title': '宿命回响', 'subtitle': '多个TA同时回应你', 'icon': Icons.auto_awesome},
      {'title': '印象匹配', 'subtitle': '千种印象任你选', 'icon': Icons.favorite},
      {'title': '自由创建', 'subtitle': '自定义你的专属恋人', 'icon': Icons.create},
      {'title': '每日限免', 'subtitle': '0点刷新', 'icon': Icons.access_time},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: functions.map((func) {
          return Column(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [AppColors.accentPurple.withOpacity(0.8), AppColors.accentBlue.withOpacity(0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Icon(func['icon'] as IconData, color: Colors.white),
              ),
              const SizedBox(height: 8),
              Text(func['title'] as String, style: const TextStyle(color: AppColors.primaryText, fontSize: 14, fontWeight: FontWeight.w500)),
              const SizedBox(height: 2),
              Text(func['subtitle'] as String, style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
            ],
          );
        }).toList(),
      ),
    );
  }

  // 构建内容区域
  Widget _buildContentSection() {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '为你推荐',
              style: TextStyle(
                color: AppColors.primaryText,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 16),
          BlocBuilder<RecommendationBloc, RecommendationState>(
            builder: (context, state) {
              if (state is RecommendationLoading) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: CircularProgressIndicator(color: AppColors.accentPurple),
                  ),
                );
              }
              if (state is RecommendationLoaded) {
                return _buildRecommendationGrid(state.feedItems);
              }
              if (state is RecommendationError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Text(
                      state.message,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  // 构建推荐内容网格
  Widget _buildRecommendationGrid(List<RecommendationItem> items) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      // --- v 核心修改 v ---
      // 使用 MasonryGridView.count 实现瀑布流
      child: MasonryGridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return _buildRecommendationCard(item);
        },
      ),
      // --- ^ 核心修改 ^ ---
    );
  }

  // 构建推荐卡片
  Widget _buildRecommendationCard(RecommendationItem item) {
    // ... (数据提取部分保持不变)
    String title = '未知内容';
    String? coverUrl;
    List<String> tags = [];
    String popularity = '0';

    if (item.type == RecommendationItemType.agent) {
        final agent = item.data as Agent;
        title = agent.name;
        coverUrl = agent.imageUrl;
        tags = agent.tags;
        popularity = _formatCount(agent.dialogueCount);
    } else {
        final story = item.data as InteractiveStoryCard;
        title = story.title;
        coverUrl = story.coverUrl;
        tags = []; // 故事暂无标签
        popularity = _formatCount(story.viewCount);
    }

  return InkWell(
    onTap: () async {
      if (item.type == RecommendationItemType.agent) {
        await _startAgentChat(context, item);
      } else {
        await _startStoryChat(context, item);
      }
    },
    borderRadius: BorderRadius.circular(16),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Stack(
        children: [
          // 背景图
          Positioned.fill(
            child: Image.network(
              coverUrl ?? 'https://via.placeholder.com/300x400?text=Error',
              fit: BoxFit.cover,
              errorBuilder: (_,__,___) => Container(color: AppColors.secondaryBg),
            ),
          ),
          // 渐变遮罩
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.transparent, Colors.black.withOpacity(0.9)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0.5, 1.0],
                ),
              ),
            ),
          ),
          // 文字信息
          Positioned(
            bottom: 12,
            left: 12,
            right: 12,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标签
                if (tags.isNotEmpty)
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: tags.take(2).map((tag) => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(tag, style: const TextStyle(color: Colors.white, fontSize: 10)),
                    )).toList(),
                  ),
                const SizedBox(height: 6),
                // 标题
                Text(
                  title,
                  style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                // 人气
                Row(
                  children: [
                    const Icon(Icons.favorite, color: AppColors.secondaryText, size: 12),
                    const SizedBox(width: 4),
                    Text(popularity, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

// 别忘了在_WatchPageState中添加 _formatCount 方法
String _formatCount(int count) {
  if (count >= 10000) {
    return '${(count / 10000).toStringAsFixed(1)}万';
  }
  return count.toString();
}

  // 开始与角色聊天
  Future<void> _startAgentChat(BuildContext context, RecommendationItem item) async {
    try {
      // 获取用户认证状态
      final authState = context.read<AuthBloc>().state;
      if (authState is! Authenticated) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请先登录')),
        );
        return;
      }

      // 获取角色ID
      final agent = item.data;
      print('DEBUG: agent data type: ${agent.runtimeType}');
      print('DEBUG: agent data: $agent');

      final agentId = _safeGetProperty(agent, 'id')?.toString();
      print('DEBUG: extracted agentId: $agentId');

      if (agentId == null) {
        print('ERROR: 无法获取角色ID，agent数据: $agent');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('角色信息错误')),
        );
        return;
      }

      // 调用API开始聊天
      final chatRepository = context.read<ChatRepository>();
      final chatId = await chatRepository.startAgentChat(agentId, authState.userId);

      if (context.mounted) {
        if (chatId != null) {
          // 跳转到聊天页面
          context.push('/chat/$chatId');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('启动聊天失败，请稍后重试')),
          );
        }
      }
    } catch (e) {
      print('ERROR: 启动聊天失败: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('启动聊天失败：$e')),
        );
      }
    }
  }

  // 开始故事互动
  Future<void> _startStoryChat(BuildContext context, RecommendationItem item) async {
    try {
      // 获取用户认证状态
      final authState = context.read<AuthBloc>().state;
      if (authState is! Authenticated) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请先登录')),
        );
        return;
      }

      // 获取故事ID
      final story = item.data;
      final storyId = _safeGetProperty(story, 'id')?.toString();
      if (storyId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('故事信息错误')),
        );
        return;
      }

      // 调用API开始故事
      final chatRepository = context.read<ChatRepository>();
      final chatId = await chatRepository.startStory(storyId, authState.userId);

      if (context.mounted) {
        if (chatId != null) {
          // 跳转到聊天页面
          context.push('/chat/$chatId');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('启动故事失败，请稍后重试')),
          );
        }
      }
    } catch (e) {
      print('ERROR: 启动故事失败: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('启动故事失败：$e')),
        );
      }
    }
  }
}

